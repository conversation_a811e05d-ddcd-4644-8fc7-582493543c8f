import { useEffect, useRef, useState } from 'react';
import { motion, useAnimation, useInView } from 'framer-motion';
import { Link } from 'react-router-dom';
import HeroSection from '../components/HeroSection';
import { StarIcon, WrenchIcon, PaintBucketIcon, DropletIcon, DiscIcon, ScanLineIcon, SparklesIcon } from 'lucide-react';
const Home = () => {
  return <main className="w-full">
      <HeroSection />
      <FeaturedServices />
      <WhyChooseUs />
      <FeaturedCars />
      <TestimonialsSection />
      <CtaSection />
    </main>;
};
const FeaturedServices = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true,
    margin: '-100px'
  });
  const controls = useAnimation();
  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 50,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };
  const services = [{
    title: 'Transmission Rebuilding',
    description: 'Make your old transmission like brand new with our expert rebuilding service, backed by a 1-year labor warranty.',
    image: 'https://images.unsplash.com/photo-1619642751034-765dfdf7c58e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1335&q=80',
    link: '/services#transmission'
  }, {
    title: 'Engine Repair',
    description: 'Complete engine overhauls and professional-grade service to keep your vehicle running at peak performance.',
    image: 'https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1335&q=80',
    link: '/services#engine'
  }, {
    title: 'Advanced Diagnostics',
    description: 'Using the latest Autel MaxiSys Ultra scan tool to diagnose and resolve even the most complex vehicle issues.',
    image: 'https://images.unsplash.com/photo-1601362840469-51e4d8d58785?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1335&q=80',
    link: '/services#diagnostics'
  }];
  return <section className="py-20 bg-slate-100">
      <div className="container mx-auto px-4 md:px-8">
        <motion.div ref={ref} variants={containerVariants} initial="hidden" animate={controls} className="text-center mb-16">
          <motion.span variants={itemVariants} className="inline-block bg-blue-100 text-blue-800 px-4 py-1 rounded-full text-sm font-semibold mb-3">
            OUR SPECIALTIES
          </motion.span>
          <motion.h2 variants={itemVariants} className="text-4xl font-bold text-gray-900 mb-4">
            Expert Automotive Services
          </motion.h2>
          <motion.p variants={itemVariants} className="text-xl text-gray-600 max-w-3xl mx-auto">
            We provide dealership quality repairs at affordable prices, with a
            focus on transmission and engine work.
          </motion.p>
        </motion.div>
        <motion.div className="grid grid-cols-1 md:grid-cols-3 gap-8" variants={containerVariants} initial="hidden" animate={controls}>
          {services.map((service, index) => <motion.div key={index} initial={{
          y: 50,
          opacity: 0
        }} animate={{
          y: 0,
          opacity: 1,
          transition: {
            duration: 0.6,
            delay: index * 0.1,
            ease: 'easeOut'
          }
        }} whileHover={{
          y: -10,
          transition: {
            type: "spring",
            stiffness: 400,
            damping: 25
          }
        }} style={{
          transform: 'translateY(0px)'
        }} className="bg-white rounded-2xl overflow-hidden shadow-xl">
              <div className="h-56 overflow-hidden">
                <img src={service.image} alt={service.title} className="w-full h-full object-cover transition-transform duration-500 hover:scale-110" />
              </div>
              <div className="p-6">
                <h3 className="text-2xl font-bold text-gray-900 mb-3">
                  {service.title}
                </h3>
                <p className="text-gray-600 mb-6">{service.description}</p>
                <Link to={service.link} className="inline-block bg-[#1e3a5f] hover:bg-blue-800 text-white font-semibold py-2 px-6 rounded-lg transition-colors">
                  Learn More
                </Link>
              </div>
            </motion.div>)}
        </motion.div>
        <motion.div variants={itemVariants} className="text-center mt-12" initial="hidden" animate={controls}>
          <Link to="/services" className="inline-block border-2 border-[#1e3a5f] text-[#1e3a5f] hover:bg-[#1e3a5f] hover:text-white font-semibold py-3 px-8 rounded-full transition-colors">
            View All Services
          </Link>
        </motion.div>
      </div>
    </section>;
};
const WhyChooseUs = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true,
    margin: '-100px'
  });
  const controls = useAnimation();
  const [activeHotspot, setActiveHotspot] = useState<number | null>(null);

  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);

  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: {
      y: 30,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    }
  };

  const hotspots = [
    {
      id: 1,
      top: '37%',
      left: '30%',
      icon: <ScanLineIcon className="h-5 w-5" />,
      title: 'Diagnostics',
      description: 'If your car needs a mobile diagnostic check done at your home or office, let AutoMechanica come to you.'
    },
    {
      id: 2,
      top: '42%',
      left: '12%',
      icon: <PaintBucketIcon className="h-5 w-5" />,
      title: 'Dent & Paint',
      description: 'AutoMechanica specializes in car dent repair and car painting services for a range of models.'
    },
    {
      id: 3,
      top: '31%',
      left: '20%',
      icon: <DropletIcon className="h-5 w-5" />,
      title: 'Oil / Lube / Filters',
      description: 'AutoMechanica proudly serves the lube, oil, and filter change needs of your vehicle, which, if done at regular intervals, extends the life of your car.'
    },
    {
      id: 4,
      top: '69%',
      left: '76%',
      icon: <DiscIcon className="h-5 w-5" />,
      title: 'Brakes',
      description: 'The brake system consists of different parts that can be fixed individually. A detailed quote is given to you after we perform our systematic brake evaluation.'
    },
    {
      id: 5,
      top: '45%',
      left: '76%',
      icon: <WrenchIcon className="h-5 w-5" />,
      title: 'Suspension',
      description: 'The suspension system of your vehicle protects you from bouncing up and down due to the bad road conditions and bumps in the road.'
    },
    {
      id: 6,
      top: '41%',
      left: '57%',
      icon: <SparklesIcon className="h-5 w-5" />,
      title: 'Detailing',
      description: 'AutoMechanica offers professional car detail services at an affordable price. Our interior cleaning, detailing, and restoration services can help you recapture that new car look and smell.'
    }
  ];

  const services = [{
    icon: <ScanLineIcon className="h-10 w-10 text-red-600" />,
    title: 'Diagnostics',
    description: 'If your car needs a mobile diagnostic check done at your home or office, let AutoMechanica come to you.',
    link: '/periodic-maintenance-service'
  }, {
    icon: <PaintBucketIcon className="h-10 w-10 text-red-600" />,
    title: 'Dent & Paint',
    description: 'AutoMechanica specializes in car dent repair and car painting services for a range of models.',
    link: '/dent-paint'
  }, {
    icon: <DropletIcon className="h-10 w-10 text-red-600" />,
    title: 'Oil / Lube / Filters',
    description: 'AutoMechanica proudly serves the Lube, Oil & Filter change needs of customers\' vehicle performance while extending the life of your vehicle.',
    link: '/transmission-and-clutch-repairs'
  }, {
    icon: <DiscIcon className="h-10 w-10 text-red-600" />,
    title: 'Brakes',
    description: 'The brake system consists of different parts that can be fixed individually. A detailed quote is given to you after we perform our systematic brake evaluation.',
    link: '/break-repairs'
  }, {
    icon: <WrenchIcon className="h-10 w-10 text-red-600" />,
    title: 'Suspension',
    description: 'The suspension system of your vehicle protects you from bouncing up and down due to the bad road conditions and bumps in the road.',
    link: '/suspension-repairs'
  }, {
    icon: <SparklesIcon className="h-10 w-10 text-red-600" />,
    title: 'Detailing',
    description: 'AutoMechanica offers professional car detail services at an affordable price. Our interior cleaning, detailing, and restoration services can help you recapture that new car look and smell.',
    link: '/9h-ceramic-coating'
  }];

  return <section className="py-20 bg-gradient-to-b from-[#1e3a5f] to-[#0f2542]">
      <div className="container mx-auto px-4 md:px-8">
        <motion.div ref={ref} variants={containerVariants} initial="hidden" animate={controls} className="text-center mb-16">
          <motion.span variants={itemVariants} className="inline-block bg-blue-900 text-blue-100 px-4 py-1 rounded-full text-sm font-semibold mb-3">
            WHY CHOOSE US
          </motion.span>
          <motion.h2 variants={itemVariants} className="text-4xl font-bold text-white mb-4">
            We Offer Full Service Auto Repair & Maintenance
          </motion.h2>
          <motion.p variants={itemVariants} className="text-xl text-blue-100 max-w-3xl mx-auto">
            Explore our comprehensive automotive services by hovering over the hotspots below
          </motion.p>
        </motion.div>

        {/* Interactive Car Section */}
        <motion.div variants={itemVariants} className="relative mb-16 max-w-4xl mx-auto">
          <div className="relative rounded-3xl p-8">
            {/* Glow effect behind the car */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 via-blue-500/30 to-blue-600/20 rounded-3xl blur-xl"></div>
            <div className="relative">
              <img 
                src="/images/cars/carbg.png" 
                alt="Car Service Areas" 
                className="w-full h-auto drop-shadow-2xl"
                style={{
                  filter: 'drop-shadow(0 25px 50px rgba(59, 130, 246, 0.3))'
                }}
                onError={(e) => {
                  // Fallback to a different car image if the local one doesn't work
                  e.currentTarget.src = 'https://images.unsplash.com/photo-1580273916550-e323be2ae537?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1464&q=80';
                }}
              />
              
              {/* Hotspots */}
              {hotspots.map((hotspot) => (
                <div
                  key={hotspot.id}
                  className="absolute"
                  style={{
                    top: hotspot.top,
                    left: hotspot.left,
                    transform: 'translate(-50%, -50%)', // Center the hotspot
                    zIndex: 40
                  }}
                  onMouseEnter={() => setActiveHotspot(hotspot.id)}
                  onMouseLeave={() => setActiveHotspot(null)}
                >
                  <motion.div
                    className="relative cursor-pointer"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {/* Hotspot Dot */}
                    <div className="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center shadow-lg border-2 border-white">
                      <motion.div
                        className="w-3 h-3 bg-white rounded-full"
                        animate={{
                          scale: [1, 1.2, 1],
                          opacity: [1, 0.8, 1]
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      />
                    </div>

                    {/* Tooltip */}
                    {activeHotspot === hotspot.id && (
                      <motion.div
                        initial={{ opacity: 0, y: 10, scale: 0.9 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        exit={{ opacity: 0, y: 10, scale: 0.9 }}
                        className="absolute z-50 pointer-events-none"
                        style={{
                          // Position bubble to the right and slightly down from hotspot
                          top: '-8px',
                          left: 'calc(100% + 12px)',
                        }}
                      >
                        <div className="bg-white rounded-lg shadow-xl relative w-60">
                          <div className="p-4">
                            <div className="flex items-center mb-2">
                              <div className="text-blue-600 mr-2">
                                {hotspot.icon}
                              </div>
                              <h6 className="font-bold text-gray-900 text-sm">
                                {hotspot.title}
                              </h6>
                            </div>
                            <p className="text-gray-600 text-sm leading-relaxed">
                              {hotspot.description}
                            </p>
                          </div>
                          {/* Arrow pointing to hotspot - positioned on upper left */}
                          <div className="absolute top-2 left-0 -translate-x-1/2">
                            <div className="w-0 h-0 border-t-[8px] border-r-[8px] border-b-[8px] border-t-transparent border-r-white border-b-transparent"></div>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </motion.div>
                </div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Service Grid */}
        <motion.div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" variants={containerVariants} initial="hidden" animate={controls}>
          {services.map((service, index) => <motion.a 
            key={index} 
            href={service.link}
            initial={{
              y: 30,
              opacity: 0
            }} 
            animate={{
              y: 0,
              opacity: 1,
              transition: {
                duration: 0.5,
                delay: index * 0.1,
                ease: 'easeOut'
              }
            }} 
            whileHover={{
              y: -5,
              backgroundColor: '#ffffff',
              transition: {
                type: "spring",
                stiffness: 400,
                damping: 25
              }
            }} 
            style={{
              transform: 'translateY(0px)'
            }} 
            className="bg-white/95 rounded-xl p-6 shadow-xl transition-all hover:shadow-2xl cursor-pointer block"
          >
              <div className="mb-4">{service.icon}</div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">
                {service.title}
              </h3>
              <p className="text-gray-600">{service.description}</p>
            </motion.a>)}
        </motion.div>
      </div>
    </section>;
};
const FeaturedCars = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true,
    margin: '-100px'
  });
  const controls = useAnimation();
  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 30,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };
  const cars = [{
    id: 1,
    title: '2019 Ford Mustang GT',
    price: '$32,500',
    image: 'https://images.unsplash.com/photo-1581650107963-3e8c1f0f0783?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
    mileage: '45,000 miles',
    featured: true
  }, {
    id: 2,
    title: '2018 Toyota Camry XSE',
    price: '$22,800',
    image: 'https://images.unsplash.com/photo-1621007947382-bb3c3994e3fb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
    mileage: '38,500 miles',
    featured: false
  }, {
    id: 3,
    title: '2020 Chevrolet Tahoe',
    price: '$42,999',
    image: 'https://images.unsplash.com/photo-1533473359331-0135ef1b58bf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
    mileage: '29,000 miles',
    featured: true
  }];
  return <section className="py-20 bg-slate-100">
      <div className="container mx-auto px-4 md:px-8">
        <motion.div ref={ref} variants={containerVariants} initial="hidden" animate={controls} className="text-center mb-16">
          <motion.span variants={itemVariants} className="inline-block bg-blue-100 text-blue-800 px-4 py-1 rounded-full text-sm font-semibold mb-3">
            FEATURED VEHICLES
          </motion.span>
          <motion.h2 variants={itemVariants} className="text-4xl font-bold text-gray-900 mb-4">
            Quality Cars for Sale
          </motion.h2>
          <motion.p variants={itemVariants} className="text-xl text-gray-600 max-w-3xl mx-auto">
            Browse our selection of quality pre-owned vehicles at competitive
            prices.
          </motion.p>
        </motion.div>
        <motion.div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" variants={containerVariants} initial="hidden" animate={controls}>
          {cars.map((car, index) => <motion.div key={index} initial={{
          y: 30,
          opacity: 0
        }} animate={{
          y: 0,
          opacity: 1,
          transition: {
            duration: 0.6,
            delay: index * 0.1,
            ease: 'easeOut'
          }
        }} whileHover={{
          y: -10,
          transition: {
            type: "spring",
            stiffness: 400,
            damping: 25
          }
        }} style={{
          transform: 'translateY(0px)'
        }} className="bg-white rounded-2xl overflow-hidden shadow-xl relative">
              {car.featured && <div className="absolute top-4 left-4 z-10">
                  <span className="bg-yellow-500 text-white text-sm font-bold px-3 py-1 rounded-full">
                    Featured
                  </span>
                </div>}
              <div className="h-56 overflow-hidden">
                <img src={car.image} alt={car.title} className="w-full h-full object-cover transition-transform duration-500 hover:scale-110" />
              </div>
              <div className="p-6">
                <div className="flex justify-between items-center mb-3">
                  <h3 className="text-xl font-bold text-gray-900">
                    {car.title}
                  </h3>
                  <span className="text-xl font-bold text-blue-700">
                    {car.price}
                  </span>
                </div>
                <p className="text-gray-600 mb-6">Mileage: {car.mileage}</p>
                <Link to={`/cars/${car.id}`} className="inline-block bg-[#1e3a5f] hover:bg-blue-800 text-white font-semibold py-2 px-6 rounded-lg transition-colors w-full text-center">
                  View Details
                </Link>
              </div>
            </motion.div>)}
        </motion.div>
        <motion.div variants={itemVariants} className="text-center mt-12" initial="hidden" animate={controls}>
          <Link to="/cars" className="inline-block border-2 border-[#1e3a5f] text-[#1e3a5f] hover:bg-[#1e3a5f] hover:text-white font-semibold py-3 px-8 rounded-full transition-colors">
            View All Cars
          </Link>
        </motion.div>
      </div>
    </section>;
};
const TestimonialsSection = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true,
    margin: '-100px'
  });
  const controls = useAnimation();
  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 30,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };
  const testimonials = [{
    name: 'Michael Rodriguez',
    role: 'Toyota Owner',
    image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=387&q=80',
    text: 'ADJ Automotive rebuilt the transmission in my Toyota and it runs better than new. Their attention to detail and expertise is unmatched. Highly recommend their services!'
  }, {
    name: 'Sarah Johnson',
    role: 'Ford F-150 Owner',
    image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=387&q=80',
    text: "I brought my F-150 in with engine issues that two other shops couldn't diagnose. ADJ found and fixed the problem in one day. Their diagnostic equipment is top-notch!"
  }, {
    name: 'David Chen',
    role: 'Mercedes Owner',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=387&q=80',
    text: "As a Mercedes owner, I was worried about finding quality service outside the dealership. ADJ's master certified technicians provided exceptional service at half the dealership price."
  }];
  return <section className="py-20 bg-gradient-to-b from-[#0f2542] to-[#1e3a5f]">
      <div className="container mx-auto px-4 md:px-8">
        <motion.div ref={ref} variants={containerVariants} initial="hidden" animate={controls} className="text-center mb-16">
          <motion.span variants={itemVariants} className="inline-block bg-blue-900 text-blue-100 px-4 py-1 rounded-full text-sm font-semibold mb-3">
            TESTIMONIALS
          </motion.span>
          <motion.h2 variants={itemVariants} className="text-4xl font-bold text-white mb-4">
            What Our Customers Say
          </motion.h2>
          <motion.p variants={itemVariants} className="text-xl text-blue-100 max-w-3xl mx-auto">
            Don't just take our word for it. Here's what our satisfied customers
            have to say.
          </motion.p>
        </motion.div>
        <motion.div className="grid grid-cols-1 md:grid-cols-3 gap-8" variants={containerVariants} initial="hidden" animate={controls}>
          {testimonials.map((testimonial, index) => <motion.div key={index} initial={{
          y: 30,
          opacity: 0
        }} animate={{
          y: 0,
          opacity: 1,
          transition: {
            duration: 0.6,
            delay: index * 0.1,
            ease: 'easeOut'
          }
        }} whileHover={{
          y: -10,
          transition: {
            type: "spring",
            stiffness: 400,
            damping: 25
          }
        }} style={{
          transform: 'translateY(0px)'
        }} className="bg-white rounded-2xl p-8 shadow-xl">
              <div className="flex items-center mb-6">
                <div className="h-16 w-16 rounded-full overflow-hidden mr-4">
                  <img src={testimonial.image} alt={testimonial.name} className="w-full h-full object-cover" />
                </div>
                <div>
                  <h4 className="text-lg font-bold text-gray-900">
                    {testimonial.name}
                  </h4>
                  <p className="text-blue-600">{testimonial.role}</p>
                </div>
              </div>
              <p className="text-gray-600 italic">"{testimonial.text}"</p>
              <div className="mt-4 flex">
                {[...Array(5)].map((_, i) => <StarIcon key={i} className="h-5 w-5 text-yellow-500" />)}
              </div>
            </motion.div>)}
        </motion.div>
      </div>
    </section>;
};
const CtaSection = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true,
    margin: '-100px'
  });
  const controls = useAnimation();
  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 30,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };
  return <section className="py-20 bg-slate-100 relative overflow-hidden">
      <div className="absolute inset-0 z-0 opacity-10">
        <div className="w-full h-full bg-cover bg-center" style={{
        backgroundImage: "url('https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1335&q=80')"
      }}></div>
      </div>
      <div className="container mx-auto px-4 md:px-8 relative z-10">
        <motion.div ref={ref} variants={containerVariants} initial="hidden" animate={controls} className="bg-[#1e3a5f] rounded-3xl p-8 md:p-16 shadow-2xl">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            <div>
              <motion.span variants={itemVariants} className="inline-block bg-blue-800 text-blue-100 px-4 py-1 rounded-full text-sm font-semibold mb-3">
                GET STARTED TODAY
              </motion.span>
              <motion.h2 variants={itemVariants} className="text-3xl md:text-4xl font-bold text-white mb-4">
                Ready to experience dealership quality repair at an affordable
                price?
              </motion.h2>
              <motion.p variants={itemVariants} className="text-lg text-blue-100 mb-8">
                Whether you need transmission work, engine repair, or any
                automotive service, our team of certified experts is ready to
                help.
              </motion.p>
              <motion.div variants={itemVariants} className="flex flex-wrap gap-4">
                <motion.button className="bg-white hover:bg-gray-100 text-[#1e3a5f] px-8 py-3 rounded-full font-semibold text-lg" whileHover={{
                scale: 1.05
              }} whileTap={{
                scale: 0.95
              }} transition={{
                type: 'spring',
                stiffness: 400,
                damping: 17
              }}>
                  Book Appointment
                </motion.button>
                <motion.a href="tel:+16714838335" className="border-2 border-white text-white hover:bg-white hover:text-[#1e3a5f] px-8 py-3 rounded-full font-semibold text-lg inline-flex items-center transition-colors" whileHover={{
                scale: 1.05
              }} whileTap={{
                scale: 0.95
              }} transition={{
                type: 'spring',
                stiffness: 400,
                damping: 17
              }}>
                  Call (*************
                </motion.a>
              </motion.div>
            </div>
            <motion.div variants={itemVariants} className="hidden md:block">
              <img src="https://images.unsplash.com/photo-1560179304-6fc1d8749b23?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80" alt="Mechanic working on car" className="rounded-xl shadow-lg w-full h-80 object-cover" />
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>;
};
export default Home;